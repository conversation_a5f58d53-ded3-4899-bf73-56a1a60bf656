table 70000 "Barcode Generator SEM"
{
    Caption = 'Barcode Generator';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Barcode Generator SEM";
    LookupPageId = "Item Barcode Generator SEM";
    Permissions =
        tabledata "Barcode Generator SEM" = R, tabledata Item = R;
    TableType = Temporary;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            AllowInCustomizations = Never;
            Caption = 'Primary Key';
            NotBlank = false;
        }
        field(2; "Barcode Text"; Code[50])
        {
            Caption = 'Barcode Text';
            ToolTip = 'Specifies the value of the Barcode Text field.';
            trigger OnValidate()
            begin
                BarcodeGeneratorMngt.ProcessBarcode(Rec);
            end;
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get("Item No.") then
                    Rec."Item Tracking Code" := Item."Item Tracking Code"
                else
                    Rec."Item Tracking Code" := '';
            end;
        }
        field(4; "Item Description"; Text[100])
        {
            CalcFormula = lookup(Item.Description where("No." = field("Item No.")));
            Caption = 'Item Description';
            Editable = false;
            FieldClass = FlowField;
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(5; "Item Tracking Code"; Code[10])
        {
            Caption = 'Item Tracking Code';
            TableRelation = "Item Tracking Code";
            ToolTip = 'Specifies the value of the Item Tracking Code field.';
            trigger OnValidate()
            begin
                BarcodeGeneratorMngt.CheckItemTrackingCodeSetStatus(Rec);
            end;
        }
        field(6; "Lot/Serial No."; Code[50])
        {
            Caption = 'Lot/Serial No.';
            ToolTip = 'Specifies the value of the Lot/Serial No. field.';
        }
        field(7; "Label Count"; Integer)
        {
            Caption = 'Label Count';
            //InitValue = 1;
            MinValue = 0;
            ToolTip = 'Specifies the value of the Label Count field.';
        }
        field(8; "Expiration Date"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the value of the Expiration Date field.';
        }
        field(9; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(10; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            TableRelation = Bin.Code where("Location Code" = const('MERKEZ'));
            ToolTip = 'Specifies the value of the Bin Code field.';
        }

        // field(9; "Print Label"; Boolean)
        // {
        //     Caption = 'Print Label';
        //     InitValue = true;
        // }
    }
    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
    var
        BarcodeGeneratorMngt: Codeunit "Barcode Generator Mngt. SEM";
}