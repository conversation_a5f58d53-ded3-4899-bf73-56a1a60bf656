table 70001 "Barcode Parsing Setup SEM"
{
    Caption = 'Barcode Parsing Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Barcode Parsing Setup SEM";
    LookupPageId = "Barcode Parsing Setup SEM";

    fields
    {
        field(1; "Code"; Code[10])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(2; Indicator; Code[10])
        {
            Caption = 'Indicator';
            ToolTip = 'Specifies the value of the Indicator field.';
        }
        field(3; "Data Lenght"; Integer)
        {
            Caption = 'Data Lenght';
            MinValue = 1;
            ToolTip = 'Specifies the value of the Data Lenght field.';
        }
        field(4; "Parsing Order"; Integer)
        {
            Caption = 'Parsing Order';
            MinValue = 1;
            ToolTip = 'Specifies the value of the Parsing Order field.';
        }
    }
    keys
    {
        key(PK; "Code")
        {
            Clustered = true;
        }
        key(SK; "Parsing Order")
        {

        }
    }
    
    fieldgroups
    {
        fieldgroup(Brick; "Code", Indicator)
        {
        }
        fieldgroup(DropDown; "Code", Indicator)
        {
        }
    }
}