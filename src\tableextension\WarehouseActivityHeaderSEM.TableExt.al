tableextension 70003 "Warehouse Activity Header SEM" extends "Warehouse Activity Header"
{
    fields
    {
        field(70000; "Item No. SEM"; Code[20])
        {
            Caption = 'Item No.';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            begin
                WarehouseManagementSEM.ValidateItemNoOnWarehouseActivityHeader(Rec);
            end;
        }
        field(70001; "Lot/Serial No. SEM"; Code[50])
        {
            Caption = 'Lot/Serial No.';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies the value of the Lot/Serial No. field.';
            trigger OnValidate()
            begin
                WarehouseManagementSEM.ValidateLotSerialNoOnWarehouseActivityHeader(Rec);
            end;
        }
        field(70002; "Bin Code SEM"; Code[20])
        {
            Caption = 'Bin Code';
            DataClassification = CustomerContent;
            TableRelation = Bin.Code where("Location Code" = field("Location Code"));
            ToolTip = 'Specifies the value of the Bin Code field.';

            trigger OnValidate()
            begin
                WarehouseManagementSEM.ValidateBinCodeOnWarehouseActivityHeader(Rec);
            end;

            trigger OnLookup()
            begin
                WarehouseManagementSEM.LookupBinCodeOnWarehouseActivityHeader(Rec);
            end;
        }
    }
    var
        WarehouseManagementSEM: Codeunit "Warehouse Management SEM";
}