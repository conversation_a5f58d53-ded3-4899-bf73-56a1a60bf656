page 70003 "Whse. Barcode Part SEM"
{
    ApplicationArea = All;
    Caption = 'Barcode Reading';
    PageType = CardPart;
    SourceTable = "Warehouse Activity Header";
    //SourceTableView = where(Type = const("Put-away"));
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(Barcode)
            {
                ShowCaption = false;

                field("Item No. SEM"; Rec."Item No. SEM")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Lot/Serial No. SEM"; Rec."Lot/Serial No. SEM")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
                field("Bin Code SEM"; Rec."Bin Code SEM")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update(true);
                    end;
                }
            }
        }
    }

}