﻿<?xml version="1.0" encoding="utf-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 xliff-core-1.2-transitional.xsd">
  <file datatype="xml" source-language="en-US" target-language="en-US" original="SEM - Item Barcode Generator">
    <body>
      <group id="body">
        <trans-unit id="Table 1764491712 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Barcode Generator</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 1174248139 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Barcode Text field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Barcode Text - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 1174248139 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Barcode Text</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Barcode Text - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 2814799924 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Bin Code field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Bin Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 2814799924 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Bin Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Bin Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 1579714849 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Expiration Date field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Expiration Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 1579714849 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Expiration Date</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Expiration Date - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 2842094365 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Item Description field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Item Description - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 2842094365 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Description</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Item Description - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 568743302 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Item No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Item No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 568743302 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Item No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 65514489 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Item Tracking Code field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Item Tracking Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 65514489 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Tracking Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Item Tracking Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 1518588695 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Label Count field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Label Count - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 1518588695 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Label Count</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Label Count - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 4196939377 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Lot/Serial No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Lot/Serial No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 4196939377 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Lot/Serial No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Lot/Serial No. - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 1663593181 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Primary Key</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Primary Key - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 2704404533 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Quantity field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Quantity - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 1764491712 - Field 2704404533 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Quantity</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Generator SEM - Field Quantity - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Barcode Parsing Setup</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 3004954119 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Code field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 3004954119 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Code - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 655952304 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Data Lenght field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Data Lenght - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 655952304 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Data Lenght</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Data Lenght - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 2929916843 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Indicator field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Indicator - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 2929916843 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Indicator</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Indicator - Property Caption</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 936590058 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Parsing Order field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Parsing Order - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Table 2360441876 - Field 936590058 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Parsing Order</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Table Barcode Parsing Setup SEM - Field Parsing Order - Property Caption</note>
        </trans-unit>
        <trans-unit id="Codeunit 3671600254 - Method 2735693345 - NamedType 2184892397" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item %1 not found.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Generator Mngt. SEM - Method CheckItemTrackingCodeSetStatus - NamedType ItemNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 3671600254 - Method 243561130 - NamedType 2184892397" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item %1 not found.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Generator Mngt. SEM - Method CreateItemJournalLine - NamedType ItemNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 3671600254 - Method 243561130 - NamedType 3488448931" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Tracking Code %1 not found.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item Tracking Code</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Generator Mngt. SEM - Method CreateItemJournalLine - NamedType ItemTrackingCodeNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 3671600254 - Method 2093760881 - NamedType 2184892397" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item not found from barcode, please select manually.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Generator Mngt. SEM - Method ProcessBarcode - NamedType ItemNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 3671600254 - Method 194007344 - NamedType 2184892397" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item %1 not found.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Generator Mngt. SEM - Method UpdateItemTrackingCodeSet - NamedType ItemNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 810595680 - Method 887451561 - NamedType 2184892397" size-unit="char" translate="yes" xml:space="preserve">
          <source>No item found with GTIN: %1</source>
          <note from="Developer" annotates="general" priority="2">%1=GTIN</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Parsing Management SEM - Method PopulateBarcodeGeneratorFromParsing - NamedType ItemNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 810595680 - Method 887451561 - NamedType 2511375334" size-unit="char" translate="yes" xml:space="preserve">
          <source>No barcode parsing setup found. Please configure at least one parsing setup.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Parsing Management SEM - Method PopulateBarcodeGeneratorFromParsing - NamedType ParsingSetupErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 810595680 - Method 3318186026 - NamedType 2184892397" size-unit="char" translate="yes" xml:space="preserve">
          <source>No item found with GTIN: %1</source>
          <note from="Developer" annotates="general" priority="2">%1=GTIN</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Parsing Management SEM - Method PopulateWarehouseReceiptHeaderFromParsing - NamedType ItemNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 810595680 - Method 3318186026 - NamedType 2511375334" size-unit="char" translate="yes" xml:space="preserve">
          <source>No barcode parsing setup found. Please configure at least one parsing setup.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Parsing Management SEM - Method PopulateWarehouseReceiptHeaderFromParsing - NamedType ParsingSetupErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 2981987453 - NamedType 2184892397" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item %1 not found.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method AssignLotSerialNoToWarehouseReceiptLine - NamedType ItemNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 2981987453 - NamedType 3488448931" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Tracking Code %1 not found for Item %2.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item Tracking Code, %2=Item No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method AssignLotSerialNoToWarehouseReceiptLine - NamedType ItemTrackingCodeNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 2981987453 - NamedType 3721949328" size-unit="char" translate="yes" xml:space="preserve">
          <source>The purchase line for warehouse receipt line %1 was not found.</source>
          <note from="Developer" annotates="general" priority="2">%1=Line No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method AssignLotSerialNoToWarehouseReceiptLine - NamedType PurchLineNotFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 1364970834 - NamedType 673954382" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item %1 with %2 %3 is already reserved for another Sales Order.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No., %2=Tracking Type, %3=Serial/Lot No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method ProcessBinCodeOnWarehouseActivityLineForPick - NamedType ItemAlreadyReservedErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 1364970834 - NamedType 1748699023" size-unit="char" translate="yes" xml:space="preserve">
          <source>No corresponding Place line found with blank Lot/Serial No. for Item %1.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method ProcessBinCodeOnWarehouseActivityLineForPick - NamedType NoBlankPlaceLineFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 1364970834 - NamedType 938035631" size-unit="char" translate="yes" xml:space="preserve">
          <source>No Take line found with blank Lot/Serial No. for Item %1.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method ProcessBinCodeOnWarehouseActivityLineForPick - NamedType NoBlankTakeLineFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 4066071868 - NamedType 2133042281" size-unit="char" translate="yes" xml:space="preserve">
          <source>No lines found for the selected %2: %1</source>
          <note from="Developer" annotates="general" priority="2">%1="Warehouse Activity Header"."Item No. SEM"; %2=FieldCaption("Item No. SEM")</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method ProcessBinCodeOnWarehouseActivityLineForPutAway - NamedType NoLinesFoundErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 2843340354 - NamedType 1473162410" size-unit="char" translate="yes" xml:space="preserve">
          <source>You have entered more quantity than is available to receive</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method UpdateWarehouseReceiptLineFromWarehouseReceiptHeader - NamedType QuantityErr</note>
        </trans-unit>
        <trans-unit id="Codeunit 1376284906 - Method 224659211 - NamedType 930125716" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item No. %1 does not exist in the Warehouse Activity Lines for %2 %3.</source>
          <note from="Developer" annotates="general" priority="2">%1=Item No., %2=Type, %3=No.</note>
          <note from="Xliff Generator" annotates="general" priority="3">Codeunit Barcode Reading Management SEM - Method ValidateItemExistsInWarehouseActivityLines - NamedType ItemNoNotFoundInLinesErr</note>
        </trans-unit>
        <trans-unit id="Page 2360441876 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Barcode Parsing Setup</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Barcode Parsing Setup SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 96721027 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Barcode Generator</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Item Barcode Generator SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 96721027 - Control 2445482498 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>General</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Item Barcode Generator SEM - Control General - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Reservation Entries</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1754090622 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Action Message Adjustment field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Action Message Adjustment - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 565903963 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Appl.-from Item Entry field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Appl.-from Item Entry - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3244334840 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Appl.-to Item Entry field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Appl.-to Item Entry - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 4221807355 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Binding field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Binding - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 4120197227 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Changed By field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Changed By - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3083748138 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Correction field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Correction - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 2369664869 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the user who created the traced record.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Created By - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1407200743 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the date on which the entry was created.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Creation Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3461834954 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies a description of the reservation entry.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Description - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1490912684 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Disallow Cancellation field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Disallow Cancellation - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 883711285 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the number of the entry, as assigned from the specified number series when the entry was created.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Entry No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3469770434 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the date on which the reserved items are expected to enter inventory.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Expected Receipt Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1579714849 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the expiration date of the lot or serial number on the item tracking line.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Expiration Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 2684044605 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Item Ledger Entry No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Item Ledger Entry No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 568743302 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the number of the item that has been reserved in this entry.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Item No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 392666526 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Item Tracking field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Item Tracking - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 2161572060 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the Location of the items that have been reserved in the entry.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Location Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1482668068 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the lot number of the item that is being handled with the associated document line.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Lot No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 781519797 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the New Expiration Date field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control New Expiration Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 891329008 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the New Lot No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control New Lot No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3028104211 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the New Package No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control New Package No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 242005773 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the New Serial No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control New Serial No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 4240543679 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the package number of the item that is being handled with the associated document line.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Package No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 751190652 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Planning Flexibility field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Planning Flexibility - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 629037869 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies that the difference is positive.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Positive - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 806193894 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies how many of the base unit of measure are contained in one unit of the item.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Qty. per Unit of Measure - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1442101329 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the quantity of item, in the base unit of measure, to be handled in a warehouse activity.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Qty. to Handle (Base) - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 2175328136 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the quantity, in the base unit of measure, that remains to be invoiced. It is calculated as Quantity - Qty. Invoiced.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Qty. to Invoice (Base) - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 2704404533 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the quantity of the record.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Quantity - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3179217921 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the quantity of the item that has been reserved in the entry.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Quantity (Base) - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3860038398 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Quantity Invoiced (Base) field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Quantity Invoiced (Base) - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3579900824 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the status of the reservation.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Reservation Status - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 47037625 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the serial number of the item that is being handled on the document line.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Serial No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 4112852368 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies when items on the document are shipped or were shipped. A shipment date is usually calculated from a requested delivery date plus lead time.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Shipment Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3263329074 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the journal batch name if the reservation entry is related to a journal or requisition line.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Source Batch Name - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 345314420 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies which source ID the reservation entry is related to.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Source ID - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 370558406 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Source Prod. Order Line field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Source Prod. Order Line - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 627063663 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies a reference number for the line, which the reservation entry is related to.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Source Ref. No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 205345179 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies which source subtype the reservation entry is related to.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Source Subtype - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1753214751 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies for which source type the reservation entry is related to.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Source Type - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 2224719403 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Suppressed Action Msg. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Suppressed Action Msg. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 577209412 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemCreatedAt field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control SystemCreatedAt - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 4000648066 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemCreatedBy field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control SystemCreatedBy - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1367528460 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemId field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control SystemId - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 2453272523 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemModifiedAt field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control SystemModifiedAt - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 4085837753 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the SystemModifiedBy field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control SystemModifiedBy - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 1241919455 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies a value when the order tracking entry is for the quantity that remains on a document line after a partial posting.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Transferred from Entry No. - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 391263709 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the value of the Untracked Surplus field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Untracked Surplus - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3450651614 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the variant of the item on the line.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Variant Code - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 4288673739 - Control 3135432252 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve">
          <source>Specifies the last day of the serial/lot number's warranty.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Reservation Entries SEM - Control Warranty Date - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="Page 626802081 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Barcode Reading</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Page Whse. Barcode Part SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="Report 3756086914 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve">
          <source>Item Label with Barcode</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">Report Item Label with Barcode SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="PageExtension 3777721638 - Control 1957191755 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Page 2447066187">
          <source>Barcode Information</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">PageExtension Warehouse Receipt SEM - Control BarcodeInfo SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="PageExtension 1368757080 - Action 3070784375 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Page 3638292657">
          <source>Toggle between showing only Place lines or all lines.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">PageExtension Whse. Put-away Subform SEM - Action FilterTakeLines SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="PageExtension 1368757080 - Action 3070784375 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Page 3638292657">
          <source>Show Only Place Lines</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">PageExtension Whse. Put-away Subform SEM - Action FilterTakeLines SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 1503060610 - Field 2044443578 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 3276313895">
          <source>Item Tracking Code Set</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Item SEM - Field Item Tracking Code Set SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3380852280 - Field 3501920173 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 809570257">
          <source>Specifies the value of the Bin Code field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Activity Header SEM - Field Bin Code SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 3380852280 - Field 3501920173 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 809570257">
          <source>Bin Code</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Activity Header SEM - Field Bin Code SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3380852280 - Field 1676234011 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 809570257">
          <source>Specifies the value of the Item No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Activity Header SEM - Field Item No. SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 3380852280 - Field 1676234011 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 809570257">
          <source>Item No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Activity Header SEM - Field Item No. SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 3380852280 - Field 732696728 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 809570257">
          <source>Specifies the value of the Lot/Serial No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Activity Header SEM - Field Lot/Serial No. SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 3380852280 - Field 732696728 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 809570257">
          <source>Lot/Serial No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Activity Header SEM - Field Lot/Serial No. SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 1536210361 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Specifies the value of the Barcode field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Barcode SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 1536210361 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Barcode</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Barcode SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 3197649448 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Specifies the value of the Expiration Date field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Expiration Date SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 3197649448 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Expiration Date</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Expiration Date SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 1676234011 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Specifies the value of the Item No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Item No. SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 1676234011 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Item No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Item No. SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 732696728 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Specifies the value of the Lot/Serial No. field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Lot/Serial No. SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 732696728 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Lot/Serial No.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Lot/Serial No. SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 1304579590 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Specifies the value of the Print Label Action field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Print Label Action SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 1304579590 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Print Label Action</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Print Label Action SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 2513607068 - Property 1295455071" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Specifies the value of the Quantity field.</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Quantity SEM - Property ToolTip</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 2513607068 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Quantity</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field Quantity SEM - Property Caption</note>
        </trans-unit>
        <trans-unit id="TableExtension 2959662879 - Field 1128086323 - Property 2879900210" size-unit="char" translate="yes" xml:space="preserve" al-object-target="Table 1003430490">
          <source>Set Focus On Barcode Field</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">TableExtension Warehouse Receipt Header SEM - Field SetFocusOnBarcodeField - Property Caption</note>
        </trans-unit>
        <trans-unit id="PermissionSet 120601064 - Property 2879900210" maxwidth="30" size-unit="char" translate="yes" xml:space="preserve">
          <source>SEM Barcode Management</source>
          <note from="Developer" annotates="general" priority="2"></note>
          <note from="Xliff Generator" annotates="general" priority="3">PermissionSet BarcodeGenerator SEM - Property Caption</note>
        </trans-unit>
      </group>
    </body>
  </file>
</xliff>