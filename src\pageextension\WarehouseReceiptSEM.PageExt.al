pageextension 70000 "Warehouse Receipt SEM" extends "Warehouse Receipt"
{
    layout
    {
        addafter(General)
        {
            //group("BarcodeReading SEM")
            //{
            //Caption = 'Barcode Reading';
            // field("Barcode SEM"; Rec."Barcode SEM")
            // {
            //     ApplicationArea = All;
            // }
            //}
            group("BarcodeInfo SEM")
            {
                Caption = 'Barcode Information';
                field("Barcode SEM"; Rec."Barcode SEM")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    begin
                        // Reset the focus flag after processing the barcode
                        Rec."SetFocusOnBarcodeField" := false;
                        CurrPage.Update(false);
                    end;
                }

                field("Item No. SEM"; Rec."Item No. SEM")
                {
                    ApplicationArea = All;
                    Editable = false;
                    ShowMandatory = true;
                }
                field("Lot/Serial No. SEM"; Rec."Lot/Serial No. SEM")
                {
                    ApplicationArea = All;
                    ShowMandatory = true;
                    trigger OnValidate()
                    begin
                        CheckFocusOnBarcode();
                    end;
                }
                field("Quantity SEM"; Rec."Quantity SEM")
                {
                    ApplicationArea = All;
                    //Editable = QuantityEditable;
                    ShowMandatory = true;
                    trigger OnValidate()
                    begin
                        CheckFocusOnBarcode();
                    end;
                }
                field("Expiration Date SEM"; Rec."Expiration Date SEM")
                {
                    ApplicationArea = All;
                    trigger OnValidate()
                    begin
                        CheckFocusOnBarcode();
                    end;
                }
                field("Print Label Action SEM"; Rec."Print Label Action SEM")
                {
                    ApplicationArea = All;
                }
            }

            usercontrol(SetFieldFocus; "SetFieldFocus SEM")
            {
                ApplicationArea = All;
                trigger Ready()
                begin
                    // Control is ready to accept focus commands
                    FieldFocusReady := true;
                    CheckFocusOnBarcode();
                end;
            }
        }
    }

    trigger OnAfterGetRecord()
    begin
        CheckFocusOnBarcode();
    end;

    trigger OnAfterGetCurrRecord()
    begin
        CheckFocusOnBarcode();
    end;

    var
        FieldFocusReady: Boolean;

    local procedure CheckFocusOnBarcode()
    begin
        if not FieldFocusReady then
            exit;

        if Rec."SetFocusOnBarcodeField" then begin
            CurrPage.SetFieldFocus.SetFocusOnField('Barcode SEM');
            Rec."SetFocusOnBarcodeField" := false;
            Rec.Modify(false);
        end;
    end;
}