codeunit 70000 "Barcode Generator Mngt. SEM"
{
    Permissions =
        tabledata "Barcode Generator SEM" = RM, tabledata Item = RM, tabledata "Item Journal Line" = RMI, tabledata "Item Tracking Code" = R;

    var
        BarcodeParsingManagement: Codeunit "Barcode Parsing Management SEM";

    procedure CheckItemTrackingCodeSetStatus(var BarcodeGenerator: Record "Barcode Generator SEM")
    var
        Item: Record Item;
        ItemNotFoundErr: Label 'Item %1 not found.', Comment = '%1=Item No.';
    begin
        if not Item.Get(BarcodeGenerator."Item No.") then
            Error(ItemNotFoundErr, BarcodeGenerator."Item No.");

        Item.TestField("Item Tracking Code Set SEM", false);
    end;

    procedure CreateItemJournalLine(var BarcodeGenerator: Record "Barcode Generator SEM")
    var
        Item: Record Item;
        ItemJournalLine: Record "Item Journal Line";
        xItemJournalLine: Record "Item Journal Line";
        ItemTrackingCode: Record "Item Tracking Code";
        ItemJournalLineNo: Integer;
        ItemNotFoundErr: Label 'Item %1 not found.', Comment = '%1=Item No.';
        ItemTrackingCodeNotFoundErr: Label 'Item Tracking Code %1 not found.', Comment = '%1=Item Tracking Code';
    begin
        if not Item.Get(BarcodeGenerator."Item No.") then
            Error(ItemNotFoundErr, BarcodeGenerator."Item No.");

        if not ItemTrackingCode.Get(Item."Item Tracking Code") then
            Error(ItemTrackingCodeNotFoundErr, Item."Item Tracking Code");

        BarcodeGenerator.TestField("Lot/Serial No.");

        if ItemTrackingCode."Man. Expir. Date Entry Reqd." then
            BarcodeGenerator.TestField("Expiration Date");

        ItemJournalLine.SetRange("Journal Template Name", 'MADDGNL');
        ItemJournalLine.SetRange("Journal Batch Name", 'MGN-VARSAY');
        if ItemJournalLine.FindLast() then begin
            ItemJournalLineNo := ItemJournalLine."Line No." + 10000;
            xItemJournalLine := ItemJournalLine;
        end
        else
            ItemJournalLineNo := 10000;

        ItemJournalLine.Init();
        ItemJournalLine."Journal Template Name" := 'MADDGNL';
        ItemJournalLine."Journal Batch Name" := 'MGN-VARSAY';
        ItemJournalLine."Line No." := ItemJournalLineNo;
        ItemJournalLine.Insert(true);
        ItemJournalLine.SetUpNewLine(xItemJournalLine);
        ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::"Positive Adjmt.");
        ItemJournalLine.Validate("Item No.", BarcodeGenerator."Item No.");
        ItemJournalLine.Validate("Location Code", 'MERKEZ');
        ItemJournalLine.Validate("Quantity", BarcodeGenerator."Quantity");
        ItemJournalLine.Validate("Bin Code", BarcodeGenerator."Bin Code");
        if ItemTrackingCode."SN Specific Tracking" then
            ItemJournalLine.Validate("Serial No.", BarcodeGenerator."Lot/Serial No.")
        else
            if ItemTrackingCode."Lot Specific Tracking" then
                ItemJournalLine.Validate("Lot No.", BarcodeGenerator."Lot/Serial No.");
        ItemJournalLine.Validate("Expiration Date", BarcodeGenerator."Expiration Date");
        ItemJournalLine.Modify(true);
    end;

    procedure ProcessBarcode(var BarcodeGenerator: Record "Barcode Generator SEM")
    var
        Item: Record Item;
        ItemNotFoundErr: Label 'Item not found from barcode, please select manually.';
    begin
        if Item.Get(CopyStr(BarcodeGenerator."Barcode Text", 1, MaxStrLen(Item."No."))) then
            BarcodeGenerator.Validate("Item No.", Item."No.")
        else
            if CopyStr(BarcodeGenerator."Barcode Text", 1, 2) = '01' then
                BarcodeParsingManagement.PopulateBarcodeGeneratorFromParsing(BarcodeGenerator)
            else
                Error(ItemNotFoundErr);

        BarcodeGenerator."Barcode Text" := '';
        BarcodeGenerator.Modify(true);
    end;

    procedure UpdateItemTrackingCodeSet(var BarcodeGenerator: Record "Barcode Generator SEM")
    var
        Item: Record Item;
        ItemNotFoundErr: Label 'Item %1 not found.', Comment = '%1=Item No.';
    begin
        if not Item.Get(BarcodeGenerator."Item No.") then
            Error(ItemNotFoundErr, BarcodeGenerator."Item No.");

        Item.Validate("Item Tracking Code", BarcodeGenerator."Item Tracking Code");
        Item."Item Tracking Code Set SEM" := true;
        Item.Modify(true);
    end;
}