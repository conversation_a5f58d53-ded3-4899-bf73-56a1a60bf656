tableextension 70001 "Warehouse Receipt Header SEM" extends "Warehouse Receipt Header"
{
    fields
    {
        field(70000; "Barcode SEM"; Code[250])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the value of the Barcode field.';
            trigger OnValidate()
            begin
                BarcodeReadingManagement.ProcessBarcodeFromWarehouseReceiptHeader(Rec);
            end;
        }
        field(70001; "Item No. SEM"; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            begin
                BarcodeReadingManagement.ValidateItemNoOnWarehouseReceiptHeader(Rec);
            end;
        }
        field(70002; "Lot/Serial No. SEM"; Code[50])
        {
            Caption = 'Lot/Serial No.';
            ToolTip = 'Specifies the value of the Lot/Serial No. field.';
            trigger OnValidate()
            begin
                BarcodeReadingManagement.UpdateWarehouseReceiptLineFromWarehouseReceiptHeader(Rec);
            end;
        }
        field(70003; "Expiration Date SEM"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the value of the Expiration Date field.';
            trigger OnValidate()
            begin
                BarcodeReadingManagement.UpdateWarehouseReceiptLineFromWarehouseReceiptHeader(Rec);
            end;
        }
        field(70004; "Quantity SEM"; Decimal)
        {
            BlankZero = true;
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
            trigger OnValidate()
            begin
                BarcodeReadingManagement.UpdateWarehouseReceiptLineFromWarehouseReceiptHeader(Rec);
            end;
        }
        field(70005; "Print Label Action SEM"; Boolean)
        {
            Caption = 'Print Label Action';
            ToolTip = 'Specifies the value of the Print Label Action field.';
            trigger OnValidate()
            begin
                BarcodeReadingManagement.OpenBarcodeGeneratorFromWarehouseReceiptHeader(Rec);
                "Print Label Action SEM" := false;
            end;
        }
        field(70006; "SetFocusOnBarcodeField"; Boolean)
        {
            Caption = 'Set Focus On Barcode Field';
            DataClassification = SystemMetadata;
        }
    }
    var
        BarcodeReadingManagement: Codeunit "Barcode Reading Management SEM";
}