codeunit 70004 "Warehouse Management SEM"
{
    Permissions =
        tabledata "Item Tracking Code" = R, tabledata "Item Tracking Setup" = R, tabledata "Warehouse Activity Header" = RM, tabledata "Warehouse Activity Line" = R;

    procedure LookupBinCodeOnWarehouseActivityHeader(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    var
        WhseItemTrackingSetup: Record "Item Tracking Setup";
        WarehouseActivityLine: Record "Warehouse Activity Line";
        WMSMgt: Codeunit "WMS Management";
        BinCode: Code[20];
        ActionType: Enum "Warehouse Action Type";
    begin
        // Exit if no item is selected
        if WarehouseActivityHeader."Item No. SEM" = '' then
            exit;

        // Determine action type based on activity type
        case WarehouseActivityHeader.Type of
            WarehouseActivityHeader.Type::"Put-away":
                ActionType := ActionType::Place;
            WarehouseActivityHeader.Type::Pick:
                ActionType := ActionType::Take;
            else
                exit;
        end;

        // Find related warehouse activity line
        WarehouseActivityLine.Reset();
        WarehouseActivityLine.SetRange("Activity Type", WarehouseActivityHeader.Type);
        WarehouseActivityLine.SetRange("No.", WarehouseActivityHeader."No.");
        WarehouseActivityLine.SetRange("Item No.", WarehouseActivityHeader."Item No. SEM");
        WarehouseActivityLine.SetRange("Action Type", ActionType);

        // Apply lot/serial no. filter if specified
        if WarehouseActivityHeader."Lot/Serial No. SEM" <> '' then begin
            WarehouseActivityLine.SetFilter("Lot No.", '%1|%2', WarehouseActivityHeader."Lot/Serial No. SEM", '');
            WarehouseActivityLine.SetFilter("Serial No.", '%1|%2', WarehouseActivityHeader."Lot/Serial No. SEM", '');
        end;

        if WarehouseActivityLine.FindFirst() then begin
            // Use the same code as in Warehouse Activity Line's FindBinCode method
            WhseItemTrackingSetup.CopyTrackingFromWhseActivityLine(WarehouseActivityLine);
            BinCode := WMSMgt.BinContentLookUp(
                WarehouseActivityLine."Location Code",
                WarehouseActivityLine."Item No.",
                WarehouseActivityLine."Variant Code",
                WarehouseActivityLine."Zone Code",
                WhseItemTrackingSetup,
                WarehouseActivityLine."Bin Code");

            if BinCode <> '' then begin
                // Update the header field
                WarehouseActivityHeader.Validate("Bin Code SEM", BinCode);
                WarehouseActivityHeader.Modify(true);
            end;
        end;
    end;

    procedure ValidateBinCodeOnWarehouseActivityHeader(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    begin
        ProcessWarehouseActivityHeaderByType(WarehouseActivityHeader);
    end;

    procedure ValidateItemNoOnWarehouseActivityHeader(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    var
        BarcodeReadingManagement: Codeunit "Barcode Reading Management SEM";
    begin
        BarcodeReadingManagement.ValidateItemExistsInWarehouseActivityLines(WarehouseActivityHeader);
        ProcessWarehouseActivityHeaderByType(WarehouseActivityHeader);
    end;

    procedure ValidateLotSerialNoOnWarehouseActivityHeader(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    begin
        ProcessWarehouseActivityHeaderByType(WarehouseActivityHeader);
    end;

    local procedure ProcessWarehouseActivityHeaderByType(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    var
        BarcodeReadingManagement: Codeunit "Barcode Reading Management SEM";
    begin
        case WarehouseActivityHeader.Type of
            WarehouseActivityHeader.Type::"Put-away":
                BarcodeReadingManagement.ProcessBinCodeOnWarehouseActivityLineForPutAway(WarehouseActivityHeader);
            WarehouseActivityHeader.Type::Pick:
                BarcodeReadingManagement.ProcessBinCodeOnWarehouseActivityLineForPick(WarehouseActivityHeader);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Tracking Management", OnAfterGetWhseItemTrkgSetupOnAfterItemTrackingCodeGet, '', false, false)]
    local procedure "Item Tracking Management_OnAfterGetWhseItemTrkgSetupOnAfterItemTrackingCodeGet"(var ItemTrackingCode: Record "Item Tracking Code"; var WhseItemTrackingSetup: Record "Item Tracking Setup" temporary)
    begin
        if ItemTrackingCode."Lot Specific Tracking" then
            WhseItemTrackingSetup."Serial No. Required" := true;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Warehouse Activity Line", OnBeforeTestTrackingIfRequired, '', false, false)]
    local procedure "Warehouse Activity Line_OnBeforeTestTrackingIfRequired"(WarehouseActivityLine: Record "Warehouse Activity Line"; WhseItemTrackingSetup: Record "Item Tracking Setup" temporary; var IsHandled: Boolean)
    begin
        if WhseItemTrackingSetup."Lot No. Required" then begin
            WarehouseActivityLine.TestField("Lot No.");
            IsHandled := true;
        end;
    end;
}