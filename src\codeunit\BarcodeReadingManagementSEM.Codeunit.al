codeunit 70002 "Barcode Reading Management SEM"
{
    Permissions =
        tabledata Item = R, tabledata "Item Tracking Code" = R, tabledata "Purchase Line" = R, tabledata "Reservation Entry" = R, tabledata "Sales Line" = R, tabledata "Warehouse Activity Header" = R, tabledata "Warehouse Activity Line" = RM, tabledata "Warehouse Receipt Header" = R, tabledata "Warehouse Receipt Line" = RM, tabledata "Warehouse Shipment Header" = R, tabledata "Warehouse Shipment Line" = RM;

    var
        BarcodeParsingManagement: Codeunit "Barcode Parsing Management SEM";
        PageManagement: Codeunit "Page Management";
        PurchLineReserve: Codeunit "Purch. Line-Reserve";

    procedure AssignLotSerialNoToWarehouseReceiptLine(WarehouseReceiptHeader: Record "Warehouse Receipt Header"; WarehouseReceiptLine: Record "Warehouse Receipt Line"; QtyToAssign: Decimal)
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        PurchaseLine: Record "Purchase Line";
        SourceTrackingSpecification: Record "Tracking Specification";
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        ItemTrackingLines: Page "Item Tracking Lines";
        ItemNotFoundErr: Label 'Item %1 not found.', Comment = '%1=Item No.';
        ItemTrackingCodeNotFoundErr: Label 'Item Tracking Code %1 not found for Item %2.', Comment = '%1=Item Tracking Code, %2=Item No.';
        PurchLineNotFoundErr: Label 'The purchase line for warehouse receipt line %1 was not found.', Comment = '%1=Line No.';
    begin
        if not Item.Get(WarehouseReceiptLine."Item No.") then
            Error(ItemNotFoundErr, WarehouseReceiptLine."Item No.");
        if not ItemTrackingCode.Get(Item."Item Tracking Code") then
            Error(ItemTrackingCodeNotFoundErr, Item."Item Tracking Code", Item."No.");

        if not PurchaseLine.Get(WarehouseReceiptLine."Source Subtype", WarehouseReceiptLine."Source No.", WarehouseReceiptLine."Line No.") then
            Error(PurchLineNotFoundErr, WarehouseReceiptLine."Line No.");

        PurchLineReserve.InitFromPurchLine(SourceTrackingSpecification, PurchaseLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification.SetItemData(WarehouseReceiptLine."Item No.", WarehouseReceiptLine.Description, WarehouseReceiptLine."Location Code", WarehouseReceiptLine."Variant Code", WarehouseReceiptLine."Bin Code", WarehouseReceiptLine."Qty. per Unit of Measure");

        if ItemTrackingCode."Lot Specific Tracking" then
            TempTrackingSpecification."Lot No." := WarehouseReceiptHeader."Lot/Serial No. SEM"
        else
            TempTrackingSpecification."Serial No." := WarehouseReceiptHeader."Lot/Serial No. SEM";

        TempTrackingSpecification.SetQuantities(WarehouseReceiptHeader."Quantity SEM" * WarehouseReceiptLine."Qty. per Unit of Measure", //QtyBase
                                                WarehouseReceiptHeader."Quantity SEM",                                                   //QtyToHandle
                                                WarehouseReceiptHeader."Quantity SEM" * WarehouseReceiptLine."Qty. per Unit of Measure", //QtyToHandleBase    
                                                WarehouseReceiptHeader."Quantity SEM",                                                   //QtyToInvoice
                                                WarehouseReceiptHeader."Quantity SEM" * WarehouseReceiptLine."Qty. per Unit of Measure", //QtyToInvoiceBase
                                                0,                                                                                       //QtyHandledBase            
                                                0);                                                                                      //QtyInvoicedBase        

        TempTrackingSpecification."Expiration Date" := WarehouseReceiptHeader."Expiration Date SEM";
        TempTrackingSpecification.Insert(false);

        ItemTrackingLines.RegisterItemTrackingLines(SourceTrackingSpecification, WorkDate(), TempTrackingSpecification);
    end;

    procedure OpenBarcodeGeneratorFromWarehouseReceiptHeader(WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        TempBarcodeGenerator: Record "Barcode Generator SEM" temporary;
    begin
        TempBarcodeGenerator.Init();
        TempBarcodeGenerator.Insert(false);
        TempBarcodeGenerator.Validate("Item No.", WarehouseReceiptHeader."Item No. SEM");
        TempBarcodeGenerator.Validate("Lot/Serial No.", WarehouseReceiptHeader."Lot/Serial No. SEM");
        TempBarcodeGenerator.Validate("Expiration Date", WarehouseReceiptHeader."Expiration Date SEM");
        TempBarcodeGenerator.Modify(false);

        PageManagement.PageRun(TempBarcodeGenerator);
    end;

    procedure ProcessBarcodeFromWarehouseReceiptHeader(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        Item: Record Item;
    begin
        WarehouseReceiptHeader."Item No. SEM" := '';
        WarehouseReceiptHeader."Quantity SEM" := 0;
        WarehouseReceiptHeader."Expiration Date SEM" := 0D;
        WarehouseReceiptHeader."Lot/Serial No. SEM" := '';

        if Item.Get(CopyStr(WarehouseReceiptHeader."Barcode SEM", 1, MaxStrLen(Item."No."))) then
            WarehouseReceiptHeader.Validate("Item No. SEM", Item."No.")
        else
            if CopyStr(WarehouseReceiptHeader."Barcode SEM", 1, 2) = '01' then
                BarcodeParsingManagement.PopulateWarehouseReceiptHeaderFromParsing(WarehouseReceiptHeader);

        WarehouseReceiptHeader."Barcode SEM" := '';
    end;

    procedure ProcessBinCodeOnWarehouseActivityLineForPick(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        ReservEntry: Record "Reservation Entry";
        PlaceWhseActivityLine: Record "Warehouse Activity Line";
        TakeWhseActivityLine: Record "Warehouse Activity Line";
        IsItemTrackingUpdated: Boolean;
        TakeLineFound: Boolean;
        ItemAlreadyReservedErr: Label 'Item %1 with %2 %3 is already reserved for another Sales Order.', Comment = '%1=Item No., %2=Tracking Type, %3=Serial/Lot No.';
        NoBlankPlaceLineFoundErr: Label 'No corresponding Place line found with blank Lot/Serial No. for Item %1.', Comment = '%1=Item No.';
        NoBlankTakeLineFoundErr: Label 'No Take line found with blank Lot/Serial No. for Item %1.', Comment = '%1=Item No.';
    begin
        if (WarehouseActivityHeader."Item No. SEM" = '') or
           (WarehouseActivityHeader."Bin Code SEM" = '') or
           (WarehouseActivityHeader."Lot/Serial No. SEM" = '') then
            exit;

        // First, try to find Take line with matching Serial/Lot No.
        TakeWhseActivityLine.SetRange("Activity Type", WarehouseActivityHeader.Type);
        TakeWhseActivityLine.SetRange("No.", WarehouseActivityHeader."No.");
        TakeWhseActivityLine.SetRange("Action Type", TakeWhseActivityLine."Action Type"::Take);
        TakeWhseActivityLine.SetRange("Item No.", WarehouseActivityHeader."Item No. SEM");

        // Try to find by Serial No.
        TakeWhseActivityLine.SetRange("Serial No.", WarehouseActivityHeader."Lot/Serial No. SEM");
        TakeLineFound := TakeWhseActivityLine.FindFirst();

        // If not found by Serial No., try by Lot No.
        if not TakeLineFound then begin
            TakeWhseActivityLine.SetRange("Serial No.", '');  // Clear Serial No. filter
            TakeWhseActivityLine.SetRange("Lot No.", WarehouseActivityHeader."Lot/Serial No. SEM");
            TakeLineFound := TakeWhseActivityLine.FindFirst();
        end;

        // If still not found, try to find a line with blank Serial/Lot No. (original logic)
        if not TakeLineFound then begin
            TakeWhseActivityLine.SetRange("Serial No.", '');
            TakeWhseActivityLine.SetRange("Lot No.", '');
            if not TakeWhseActivityLine.FindFirst() then
                Error(NoBlankTakeLineFoundErr, WarehouseActivityHeader."Item No. SEM");
        end;

        // Check if item with this serial/lot number is already reserved for another sales order
        // Only needed if we're assigning a new tracking number
        if not TakeLineFound and (WarehouseActivityHeader."Item No. SEM" <> '') and
           (WarehouseActivityHeader."Lot/Serial No. SEM" <> '') then begin
            ReservEntry.Reset();
            ReservEntry.SetRange("Item No.", WarehouseActivityHeader."Item No. SEM");
            ReservEntry.SetRange("Source Type", DATABASE::"Sales Line");
            ReservEntry.SetFilter("Source ID", '<>%1', TakeWhseActivityLine."Source No.");
            ReservEntry.SetRange("Reservation Status", ReservEntry."Reservation Status"::Reservation);
            ReservEntry.SetRange(Positive, false);

            // Check for Serial No.
            ReservEntry.SetRange("Serial No.", WarehouseActivityHeader."Lot/Serial No. SEM");
            if not ReservEntry.IsEmpty() then
                Error(ItemAlreadyReservedErr, WarehouseActivityHeader."Item No. SEM", TakeWhseActivityLine.FieldCaption("Serial No."), WarehouseActivityHeader."Lot/Serial No. SEM");

            // Check for Lot No.
            ReservEntry.SetRange("Serial No.", '');  // Clear Serial No. filter
            ReservEntry.SetRange("Lot No.", WarehouseActivityHeader."Lot/Serial No. SEM");
            if not ReservEntry.IsEmpty() then
                Error(ItemAlreadyReservedErr, WarehouseActivityHeader."Item No. SEM", TakeWhseActivityLine.FieldCaption("Lot No."), WarehouseActivityHeader."Lot/Serial No. SEM");
        end;

        // Update Take line
        IsItemTrackingUpdated := false;
        if Item.Get(WarehouseActivityHeader."Item No. SEM") then
            if ItemTrackingCode.Get(Item."Item Tracking Code") then
                if not TakeLineFound then begin  // Only update tracking if we found a blank line
                    if ItemTrackingCode."SN Specific Tracking" then begin
                        TakeWhseActivityLine.Validate("Serial No.", WarehouseActivityHeader."Lot/Serial No. SEM");
                        IsItemTrackingUpdated := true;
                    end else
                        if ItemTrackingCode."Lot Specific Tracking" then begin
                            TakeWhseActivityLine.Validate("Lot No.", WarehouseActivityHeader."Lot/Serial No. SEM");
                            IsItemTrackingUpdated := true;
                        end;
                end;

        // Always update the bin code
        TakeWhseActivityLine.Validate("Bin Code", WarehouseActivityHeader."Bin Code SEM");
        TakeWhseActivityLine.Modify(true);

        // Find the corresponding Place line
        PlaceWhseActivityLine.SetRange("Activity Type", WarehouseActivityHeader.Type);
        PlaceWhseActivityLine.SetRange("No.", WarehouseActivityHeader."No.");
        PlaceWhseActivityLine.SetRange("Action Type", PlaceWhseActivityLine."Action Type"::Place);
        PlaceWhseActivityLine.SetRange("Item No.", WarehouseActivityHeader."Item No. SEM");

        // Look for Place line with same tracking as take line (if tracking was already set)
        if TakeLineFound then begin
            if TakeWhseActivityLine."Serial No." <> '' then
                PlaceWhseActivityLine.SetRange("Serial No.", TakeWhseActivityLine."Serial No.")
            else
                if TakeWhseActivityLine."Lot No." <> '' then
                    PlaceWhseActivityLine.SetRange("Lot No.", TakeWhseActivityLine."Lot No.");

            if not PlaceWhseActivityLine.FindFirst() then begin
                // If not found with tracking, look for blank tracking
                PlaceWhseActivityLine.SetRange("Serial No.", '');
                PlaceWhseActivityLine.SetRange("Lot No.", '');
                if not PlaceWhseActivityLine.FindFirst() then
                    Error(NoBlankPlaceLineFoundErr, WarehouseActivityHeader."Item No. SEM");
            end;
        end else begin
            // Original logic - find blank Place line
            PlaceWhseActivityLine.SetFilter("Serial No.", '%1', '');
            PlaceWhseActivityLine.SetFilter("Lot No.", '%1', '');
            if not PlaceWhseActivityLine.FindFirst() then
                Error(NoBlankPlaceLineFoundErr, WarehouseActivityHeader."Item No. SEM");
        end;

        // Update Place line (only item tracking if needed)
        if IsItemTrackingUpdated then // Only update if we changed tracking on Take line
            if ItemTrackingCode."SN Specific Tracking" then
                PlaceWhseActivityLine.Validate("Serial No.", WarehouseActivityHeader."Lot/Serial No. SEM")
            else
                if ItemTrackingCode."Lot Specific Tracking" then
                    PlaceWhseActivityLine.Validate("Lot No.", WarehouseActivityHeader."Lot/Serial No. SEM");

        PlaceWhseActivityLine.Modify(true);

        // Clear fields after processing
        WarehouseActivityHeader."Item No. SEM" := '';
        WarehouseActivityHeader."Bin Code SEM" := '';
        WarehouseActivityHeader."Lot/Serial No. SEM" := '';
    end;

    procedure ProcessBinCodeOnWarehouseActivityLineForPutAway(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    var
        WarehouseActivityLine: Record "Warehouse Activity Line";
        NoLinesFoundErr: Label 'No lines found for the selected %2: %1', Comment = '%1="Warehouse Activity Header"."Item No. SEM"; %2=FieldCaption("Item No. SEM")';
    begin
        if (WarehouseActivityHeader."Item No. SEM" = '') or
           (WarehouseActivityHeader."Bin Code SEM" = '') or
           (WarehouseActivityHeader."Lot/Serial No. SEM" = '') then
            exit;

        WarehouseActivityLine.SetRange("Activity Type", WarehouseActivityHeader.Type);
        WarehouseActivityLine.SetRange("No.", WarehouseActivityHeader."No.");
        WarehouseActivityLine.SetRange("Action Type", WarehouseActivityLine."Action Type"::Place);
        WarehouseActivityLine.SetRange("Item No.", WarehouseActivityHeader."Item No. SEM");
        if not WarehouseActivityLine.FindFirst() then
            Error(NoLinesFoundErr, WarehouseActivityHeader."Item No. SEM", WarehouseActivityHeader.FieldCaption("Item No. SEM"));
        if WarehouseActivityLine."Serial No." <> '' then
            WarehouseActivityLine.SetRange("Serial No.", WarehouseActivityHeader."Lot/Serial No. SEM")
        else
            WarehouseActivityLine.SetRange("Lot No.", WarehouseActivityHeader."Lot/Serial No. SEM");
        if not WarehouseActivityLine.FindFirst() then
            Error(NoLinesFoundErr, WarehouseActivityHeader."Lot/Serial No. SEM", WarehouseActivityHeader.FieldCaption("Lot/Serial No. SEM"));

        WarehouseActivityLine.Validate("Bin Code", WarehouseActivityHeader."Bin Code SEM");
        WarehouseActivityLine.Modify(true);

        WarehouseActivityHeader."Item No. SEM" := '';
        WarehouseActivityHeader."Bin Code SEM" := '';
        WarehouseActivityHeader."Lot/Serial No. SEM" := '';
    end;

    procedure UpdateWarehouseReceiptLineFromWarehouseReceiptHeader(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
        CurrPage: Page "Warehouse Receipt";
        CalculatedOutstandingQty: Decimal;
        TotalQtyToAssign: Decimal;
        QuantityErr: Label 'You have entered more quantity than is available to receive';
    begin
        if not IsWarehouseLineValid(WarehouseReceiptHeader) then
            exit;

        TotalQtyToAssign := WarehouseReceiptHeader."Quantity SEM";

        WarehouseReceiptLine.SetRange("No.", WarehouseReceiptHeader."No.");
        WarehouseReceiptLine.SetRange("Item No.", WarehouseReceiptHeader."Item No. SEM");
        WarehouseReceiptLine.FindSet(true);
        repeat
            CalculatedOutstandingQty := WarehouseReceiptLine.Quantity - WarehouseReceiptLine."Qty. to Receive" - WarehouseReceiptLine."Qty. Received";
            if (CalculatedOutstandingQty > 0) and (TotalQtyToAssign > 0) then
                if CalculatedOutstandingQty > TotalQtyToAssign then begin
                    WarehouseReceiptLine.Validate("Qty. to Receive", WarehouseReceiptLine."Qty. to Receive" + TotalQtyToAssign);
                    WarehouseReceiptLine.Modify(true);

                    AssignLotSerialNoToWarehouseReceiptLine(WarehouseReceiptHeader, WarehouseReceiptLine, TotalQtyToAssign);

                    TotalQtyToAssign := 0;
                end
                else begin
                    WarehouseReceiptLine.Validate("Qty. to Receive", WarehouseReceiptLine."Qty. to Receive" + CalculatedOutstandingQty);
                    WarehouseReceiptLine.Modify(true);

                    AssignLotSerialNoToWarehouseReceiptLine(WarehouseReceiptHeader, WarehouseReceiptLine, CalculatedOutstandingQty);

                    TotalQtyToAssign := TotalQtyToAssign - CalculatedOutstandingQty;
                end;
        until (TotalQtyToAssign = 0) or (WarehouseReceiptLine.Next() = 0);

        if TotalQtyToAssign > 0 then
            Error(QuantityErr);

        // Set the cursor focus to the Barcode field when everything is validated successfully
        // This will be captured by the page and used to set focus
        WarehouseReceiptHeader.SetFocusOnBarcodeField := true;
    end;

    procedure ValidateItemExistsInWarehouseActivityLines(var WarehouseActivityHeader: Record "Warehouse Activity Header")
    var
        WarehouseActivityLine: Record "Warehouse Activity Line";
        ItemNo: Code[20];
        ItemNoNotFoundInLinesErr: Label 'Item No. %1 does not exist in the Warehouse Activity Lines for %2 %3.', Comment = '%1=Item No., %2=Type, %3=No.';
    begin
        if WarehouseActivityHeader."Item No. SEM" = '' then
            exit;

        ItemNo := WarehouseActivityHeader."Item No. SEM"; // Store the Item No. for the error message

        WarehouseActivityLine.SetRange("Activity Type", WarehouseActivityHeader.Type);
        WarehouseActivityLine.SetRange("No.", WarehouseActivityHeader."No.");
        WarehouseActivityLine.SetRange("Item No.", ItemNo);

        if WarehouseActivityLine.IsEmpty then begin
            WarehouseActivityHeader."Item No. SEM" := '';
            Error(ItemNoNotFoundInLinesErr, ItemNo, WarehouseActivityHeader.Type, WarehouseActivityHeader."No.");
        end;
    end;

    procedure ValidateItemNoOnWarehouseReceiptHeader(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
    begin
        if Item.Get(WarehouseReceiptHeader."Item No. SEM") then
            if ItemTrackingCode.Get(Item."Item Tracking Code") then
                if ItemTrackingCode."SN Specific Tracking" then
                    WarehouseReceiptHeader."Quantity SEM" := 1;
    end;

    local procedure IsWarehouseLineValid(var WarehouseReceiptHeader: Record "Warehouse Receipt Header"): Boolean
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        ItemFoundResult: Boolean;
        ItemTrackingCodeFoundResult: Boolean;
        Zero: Decimal;
        EmptyString: Text;
    begin
        // Define constants
        EmptyString := '';
        Zero := 0;

        // First check basic requirements
        if (WarehouseReceiptHeader."Item No. SEM" = EmptyString) or
            (WarehouseReceiptHeader."Quantity SEM" = Zero) or
            (WarehouseReceiptHeader."Lot/Serial No. SEM" = EmptyString) then
            exit(false);

        // Check if item exists
        ItemFoundResult := Item.Get(WarehouseReceiptHeader."Item No. SEM");
        if not ItemFoundResult then
            exit(false);

        // Check if item tracking code exists
        ItemTrackingCodeFoundResult := ItemTrackingCode.Get(Item."Item Tracking Code");
        if not ItemTrackingCodeFoundResult then
            exit(false);

        // Check expiration date requirement
        exit(not ((ItemTrackingCode."Man. Expir. Date Entry Reqd.") and
                  (WarehouseReceiptHeader."Expiration Date SEM" = 0D)));
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Pick", OnBeforeWhseActivLineInsert, '', false, false)]
    local procedure "Create Pick_OnBeforeWhseActivLineInsert"(var WarehouseActivityLine: Record "Warehouse Activity Line"; WarehouseActivityHeader: Record "Warehouse Activity Header"; var IsHandled: Boolean)
    begin
        WarehouseActivityLine."Bin Code" := '';
        WarehouseActivityLine."Zone Code" := '';
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Put-away", OnAfterAssignPlaceBinZone, '', false, false)]
    local procedure "Create Put-away_OnAfterAssignPlaceBinZone"(var WarehouseActivityLine: Record "Warehouse Activity Line")
    begin
        WarehouseActivityLine."Bin Code" := '';
        WarehouseActivityLine."Zone Code" := '';
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterCreateWhseReceiptHeaderFromWhseRequest, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterCreateWhseReceiptHeaderFromWhseRequest"(var WhseReceiptHeader: Record "Warehouse Receipt Header"; var WarehouseRequest: Record "Warehouse Request"; var GetSourceDocuments: Report "Get Source Documents")
    var
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        WarehouseReceiptLine.SetRange("No.", WhseReceiptHeader."No.");
        WarehouseReceiptLine.DeleteQtyToReceive(WarehouseReceiptLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Outbound", OnAfterCreateWhseShipmentHeaderFromWhseRequest, '', false, false)]
    local procedure "Get Source Doc. Outbound_OnAfterCreateWhseShptHeaderFromWhseRequest"(var WhseShptHeader: Record "Warehouse Shipment Header"; var WarehouseRequest: Record "Warehouse Request")
    var
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
    begin
        WarehouseShipmentLine.SetRange("No.", WhseShptHeader."No.");
        WarehouseShipmentLine.DeleteQtyToHandle(WarehouseShipmentLine);
    end;
}