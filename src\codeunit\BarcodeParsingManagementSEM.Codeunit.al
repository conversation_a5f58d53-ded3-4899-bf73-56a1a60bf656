codeunit 70001 "Barcode Parsing Management SEM"
{
    Permissions =
        tabledata "Barcode Generator SEM" = R, tabledata "Barcode Parsing Setup SEM" = R, tabledata Item = R, tabledata "Warehouse Receipt Header" = R, tabledata "Warehouse Shipment Header" = R;

    trigger OnRun()
    var
        BarcodeGenerator: Record "Barcode Generator SEM";
    begin
        PopulateBarcodeGeneratorFromParsing(BarcodeGenerator);
    end;

    procedure PopulateBarcodeGeneratorFromParsing(var BarcodeGenerator: Record "Barcode Generator SEM")
    var
        BarcodeParsingSetup: Record "Barcode Parsing Setup SEM";
        Item: Record Item;
        GTIN: Code[14];
        LotNo: Code[50];
        ExpirationDate: Date;
        Day: Integer;
        Month: Integer;
        Year: Integer;
        ItemNotFoundErr: Label 'No item found with GTIN: %1', Comment = '%1=GTIN';
        ParsingSetupErr: Label 'No barcode parsing setup found. Please configure at least one parsing setup.';
        BarcodeTxt: Text;
        ExpirationDateText: Text;
    begin
        if not BarcodeParsingSetup.SetCurrentKey("Parsing Order") then
            Error(ParsingSetupErr);

        if not BarcodeParsingSetup.FindSet(false) then
            Error(ParsingSetupErr);

        //0105700572029519172405311041464037
        BarcodeTxt := BarcodeGenerator."Barcode Text";
        repeat
            //Message(Format(StrPos('0105700572029519172405311041464037', BarcodeParsingSetup.Indicator)));

            //Message(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"));
            case BarcodeParsingSetup.Indicator of
                '01':
                    GTIN := CopyStr(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"), 1, MaxStrLen(GTIN));
                '17':
                    begin
                        ExpirationDateText := CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght");

                        Evaluate(Year, CopyStr(ExpirationDateText, 1, 2));
                        Evaluate(Month, CopyStr(ExpirationDateText, 3, 2));
                        Evaluate(Day, CopyStr(ExpirationDateText, 5, 2));

                        ExpirationDate := DMY2Date(Day, Month, Year + 2000);
                    end;
                '10':
                    LotNo := CopyStr(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"), 1, MaxStrLen(LotNo));
            end;
            BarcodeTxt := DelStr(BarcodeTxt, 1, StrLen(BarcodeParsingSetup.Indicator) + BarcodeParsingSetup."Data Lenght");
        until BarcodeParsingSetup.Next() = 0;

        Item.SetRange(GTIN, GTIN);
        if not Item.FindFirst() then
            Error(ItemNotFoundErr, GTIN);

        BarcodeGenerator.Validate("Item No.", Item."No.");
        BarcodeGenerator.Validate("Lot/Serial No.", LotNo);
        BarcodeGenerator.Validate("Expiration Date", ExpirationDate);
    end;

    procedure PopulateWarehouseReceiptHeaderFromParsing(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        BarcodeParsingSetup: Record "Barcode Parsing Setup SEM";
        Item: Record Item;
        GTIN: Code[14];
        LotNo: Code[50];
        ExpirationDate: Date;
        Day: Integer;
        Month: Integer;
        Year: Integer;
        ItemNotFoundErr: Label 'No item found with GTIN: %1', Comment = '%1=GTIN';
        ParsingSetupErr: Label 'No barcode parsing setup found. Please configure at least one parsing setup.';
        BarcodeTxt: Text;
        ExpirationDateText: Text;
    begin
        if not BarcodeParsingSetup.SetCurrentKey("Parsing Order") then
            Error(ParsingSetupErr);

        if not BarcodeParsingSetup.FindSet(false) then
            Error(ParsingSetupErr);

        //0105700572029519172405311041464037
        BarcodeTxt := WarehouseReceiptHeader."Barcode SEM";
        repeat
            //Message(Format(StrPos('0105700572029519172405311041464037', BarcodeParsingSetup.Indicator)));

            //Message(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"));
            case BarcodeParsingSetup.Indicator of
                '01':
                    GTIN := CopyStr(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"), 1, MaxStrLen(GTIN));
                '17':
                    begin
                        ExpirationDateText := CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght");

                        Evaluate(Year, CopyStr(ExpirationDateText, 1, 2));
                        Evaluate(Month, CopyStr(ExpirationDateText, 3, 2));
                        Evaluate(Day, CopyStr(ExpirationDateText, 5, 2));

                        ExpirationDate := DMY2Date(Day, Month, Year + 2000);
                    end;
                '10':
                    LotNo := CopyStr(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"), 1, MaxStrLen(LotNo));
            end;
            BarcodeTxt := DelStr(BarcodeTxt, 1, StrLen(BarcodeParsingSetup.Indicator) + BarcodeParsingSetup."Data Lenght");
        until BarcodeParsingSetup.Next() = 0;

        Item.SetRange(GTIN, GTIN);
        if not Item.FindFirst() then
            Error(ItemNotFoundErr, GTIN);

        WarehouseReceiptHeader.Validate("Item No. SEM", Item."No.");
        WarehouseReceiptHeader.Validate("Expiration Date SEM", ExpirationDate);
        WarehouseReceiptHeader.Validate("Lot/Serial No. SEM", LotNo);
    end;

    // procedure PopulateWarehouseShipmentHeaderFromParsing(var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    // var
    //     BarcodeParsingSetup: Record "Barcode Parsing Setup SEM";
    //     Item: Record Item;
    //     GTIN: Code[14];
    //     LotNo: Code[50];
    //     ExpirationDate: Date;
    //     Day: Integer;
    //     Month: Integer;
    //     Year: Integer;
    //     ItemNotFoundErr: Label 'No item found with GTIN: %1', Comment = '%1=GTIN';
    //     ParsingSetupErr: Label 'No barcode parsing setup found. Please configure at least one parsing setup.';
    //     BarcodeTxt: Text;
    //     ExpirationDateText: Text;
    // begin
    //     if not BarcodeParsingSetup.SetCurrentKey("Parsing Order") then
    //         Error(ParsingSetupErr);

    //     if not BarcodeParsingSetup.FindSet(false) then
    //         Error(ParsingSetupErr);

    //     BarcodeTxt := WarehouseShipmentHeader."Barcode SEM";
    //     repeat
    //         case BarcodeParsingSetup.Indicator of
    //             '01':
    //                 GTIN := CopyStr(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"), 1, MaxStrLen(GTIN));
    //             '17':
    //                 begin
    //                     ExpirationDateText := CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght");

    //                     Evaluate(Year, CopyStr(ExpirationDateText, 1, 2));
    //                     Evaluate(Month, CopyStr(ExpirationDateText, 3, 2));
    //                     Evaluate(Day, CopyStr(ExpirationDateText, 5, 2));

    //                     ExpirationDate := DMY2Date(Day, Month, Year + 2000);
    //                 end;
    //             '10':
    //                 LotNo := CopyStr(CopyStr(BarcodeTxt, StrPos(BarcodeTxt, BarcodeParsingSetup.Indicator) + StrLen(BarcodeParsingSetup.Indicator), BarcodeParsingSetup."Data Lenght"), 1, MaxStrLen(LotNo));
    //         end;
    //         BarcodeTxt := DelStr(BarcodeTxt, 1, StrLen(BarcodeParsingSetup.Indicator) + BarcodeParsingSetup."Data Lenght");
    //     until BarcodeParsingSetup.Next() = 0;

    //     Item.SetRange(GTIN, GTIN);
    //     if not Item.FindFirst() then
    //         Error(ItemNotFoundErr, GTIN);

    //     WarehouseShipmentHeader.Validate("Item No. SEM", Item."No.");
    //     WarehouseShipmentHeader.Validate("Expiration Date SEM", ExpirationDate);
    //     WarehouseShipmentHeader.Validate("Lot/Serial No. SEM", LotNo);
    // end;
}