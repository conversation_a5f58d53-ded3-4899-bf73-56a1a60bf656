{
    "alOutline.additionalMandatoryAffixesPatterns": [
        " INF",
        " SEM"
    ],
    "alOutline.defaultDataClassification": "CustomerContent",
    "alOutline.enableCodeCopFixes": true,
    "alOutline.fixCaseRemovesQuotesFromDataTypeIdentifiers": true,
    "alOutline.fixCodeCopMissingParenthesesOnSave": true,
    "alOutline.noEmptyLinesAtTheEndOfWizardGeneratedFiles": true,
    "alOutline.openDefinitionInNewTab": true,
    "linterCop.load-pre-releases": true,
    "alVarHelper.ignoreALSuffix": "SEM",
    "alNavigator.ignoreALSuffix": "SEM",
    "CRS.OnSaveAlFileAction": "Reorganize",
    "CRS.ObjectNameSuffix": " SEM",
    "al.codeAnalyzers": [
        "${CodeCop}",
        "${UICop}",
        "${PerTenantExtensionCop}",
        "${analyzerFolder}BusinessCentral.LinterCop.dll"
    ],
    "al.ruleSetPath": "infotek.ruleset.json",
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit"
    },
    "editor.snippetSuggestions": "bottom",
    "ALTB.snippetTargetLanguage": "TRK",
    "xliffSync.snippetTargetLanguage": "TRK",
}