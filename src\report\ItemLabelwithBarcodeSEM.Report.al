report 70000 "Item Label with Barcode SEM"
{
    ApplicationArea = All;
    Caption = 'Item Label with Barcode';
    DefaultLayout = RDLC;
    RDLCLayout = 'x.rdlc';
    UsageCategory = ReportsAndAnalysis;

    dataset
    {
        dataitem(CopyLoop; Integer)
        {
            dataitem(TempBarcodeGenerator; "Barcode Generator SEM")
            {
                CalcFields = "Item Description";
                column(ExpirationDate_TempBarcodeGenerator; "Expiration Date")
                {
                }
                column(ItemDescription; "Item Description")
                {
                }
                column(ItemNo; "Item No.")
                {
                }
                column(ItemNoBarcode; ItemNoBarcode)
                {
                }
                column(ItemTrackingCode_BarcodeGeneratorSEM; "Item Tracking Code")
                {
                }
                column(LotSerialNo; "Lot/Serial No.")
                {
                }
                column(LotSerialNoBarcode; LotSerialNoBarcode)
                {
                }
                trigger OnAfterGetRecord()
                begin
                    BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                    BarcodeSymbology := Enum::"Barcode Symbology"::Code128;

                    ItemNoBarcode := BarcodeFontProvider.EncodeFont("Item No.", BarcodeSymbology);
                    LotSerialNoBarcode := BarcodeFontProvider.EncodeFont("Lot/Serial No.", BarcodeSymbology);

                    if not (TempBarcodeGenerator.Next() = 0) then
                        CurrReport.Break();
                end;
            }
            trigger OnPreDataItem()
            begin
                SetRange(Number, 1, CopyCountInteger);
            end;
        }
    }
    // trigger OnPostReport()
    // begin
    //     BarcodeGeneratorMngt.UpdateItemTrackingCodeSet(TempBarcodeGenerator);
    // end;

    var
        //BarcodeGeneratorMngt: Codeunit "Barcode Generator Mngt. SEM";
        BarcodeSymbology: Enum "Barcode Symbology";
        CopyCountInteger: Integer;
        BarcodeFontProvider: Interface "Barcode Font Provider";
        ItemNoBarcode: Text;
        LotSerialNoBarcode: Text;

    procedure GetCopyCount(ParamCopyCount: Integer)
    begin
        CopyCountInteger := ParamCopyCount;
    end;

    procedure PopulateTempTable(var ParameterBarcodeGenerator: Record "Barcode Generator SEM")
    begin
        TempBarcodeGenerator := ParameterBarcodeGenerator;
        TempBarcodeGenerator.Insert(false);
    end;
}