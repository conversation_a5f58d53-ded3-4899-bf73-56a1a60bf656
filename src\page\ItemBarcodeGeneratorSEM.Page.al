page 70000 "Item Barcode Generator SEM"
{
    ApplicationArea = All;
    Caption = 'Item Barcode Generator';
    PageType = StandardDialog;
    SourceTable = "Barcode Generator SEM";

    SourceTableTemporary = true;

    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("Bin Code"; Rec."Bin Code")
                {
                }
                field("Barcode Text"; Rec."Barcode Text")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Item No."; Rec."Item No.")
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Item Tracking Code"; Rec."Item Tracking Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }

                field("Lot/Serial No."; Rec."Lot/Serial No.")
                {
                    trigger OnAssistEdit()
                    var
                        // Item: Record Item;
                        // ItemTrackingCode: Record "Item Tracking Code";
                        NoSeries: Record "No. Series";
                        NoSeriesManagement: Codeunit "No. Series";
                    begin
                        NoSeries.SetFilter(Code, '%1|%2', '*LOT*', '*SERINO');

                        if Page.RunModal(Page::"No. Series", NoSeries) = Action::LookupOK then
                            Rec."Lot/Serial No." := NoSeriesManagement.GetNextNo(NoSeries.Code, WorkDate(), false);
                    end;
                }
                field("Expiration Date"; Rec."Expiration Date")
                {
                }
                field("Label Count"; Rec."Label Count")
                {
                }

            }
        }
    }
    trigger OnOpenPage()
    begin
        if not Rec.Get() then begin
            Rec.Init();
            Rec."Bin Code" := BinCode;
            Rec.Insert(false);
        end;

    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        ItemLabelwithBarcode: Report "Item Label with Barcode SEM";
        ItemBarcodeGenerator: Page "Item Barcode Generator SEM";
    begin
        if CloseAction <> CloseAction::OK then
            exit;

        //Print Barcode here
        if Rec."Label Count" > 0 then begin
            ItemLabelwithBarcode.PopulateTempTable(Rec);
            ItemLabelwithBarcode.GetCopyCount(Rec."Label Count");
            ItemLabelwithBarcode.Run();
        end;

        //BarcodeGeneratorMngt.UpdateItemTrackingCodeSet(Rec);

        //BarcodeGeneratorMngt.CreateItemJournalLine(Rec);

        ItemBarcodeGenerator.SetBinCode(Rec."Bin Code");
        ItemBarcodeGenerator.Run();
        //Page.Run(Page::"Item Barcode Generator SEM");
    end;

    var
        //BarcodeGeneratorMngt: Codeunit "Barcode Generator Mngt. SEM";
        BinCode: Code[20];

    procedure SetBinCode(ParamBinCode: Code[20])
    begin
        BinCode := ParamBinCode;
    end;

}